/**
 * Loading components for various UI states
 */

import React from 'react';
import { cn } from '@/lib/utils';
import { Loader2 } from 'lucide-react';

/**
 * Props for Spinner component
 */
interface SpinnerProps {
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

/**
 * Spinner component
 */
export function Spinner({ size = 'md', className }: SpinnerProps) {
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-6 w-6',
    lg: 'h-8 w-8',
  };

  return (
    <Loader2 
      className={cn(
        'animate-spin',
        sizeClasses[size],
        className
      )} 
    />
  );
}

/**
 * Props for LoadingButton component
 */
interface LoadingButtonProps extends React.ComponentProps<'button'> {
  isLoading?: boolean;
  loadingText?: string;
  children: React.ReactNode;
}

/**
 * Button with loading state
 */
export function LoadingButton({ 
  isLoading = false, 
  loadingText, 
  children, 
  disabled,
  className,
  ...props 
}: LoadingButtonProps) {
  return (
    <button
      {...props}
      disabled={disabled || isLoading}
      className={cn(
        'inline-flex items-center justify-center gap-2',
        className
      )}
    >
      {isLoading && <Spinner size="sm" />}
      {isLoading && loadingText ? loadingText : children}
    </button>
  );
}

/**
 * Props for LoadingCard component
 */
interface LoadingCardProps {
  title?: string;
  description?: string;
  className?: string;
}

/**
 * Card with loading state
 */
export function LoadingCard({ 
  title = 'Loading...', 
  description = 'Please wait while we load your content.',
  className 
}: LoadingCardProps) {
  return (
    <div className={cn(
      'flex flex-col items-center justify-center p-8 text-center',
      className
    )}>
      <Spinner size="lg" className="mb-4" />
      <h3 className="text-lg font-semibold mb-2">{title}</h3>
      <p className="text-muted-foreground">{description}</p>
    </div>
  );
}

/**
 * Props for LoadingOverlay component
 */
interface LoadingOverlayProps {
  isVisible: boolean;
  title?: string;
  description?: string;
  className?: string;
}

/**
 * Full-screen loading overlay
 */
export function LoadingOverlay({ 
  isVisible, 
  title = 'Loading...', 
  description = 'Please wait...',
  className 
}: LoadingOverlayProps) {
  if (!isVisible) return null;

  return (
    <div className={cn(
      'fixed inset-0 z-50 flex items-center justify-center bg-background/80 backdrop-blur-sm',
      className
    )}>
      <div className="flex flex-col items-center justify-center p-8 text-center">
        <Spinner size="lg" className="mb-4" />
        <h3 className="text-lg font-semibold mb-2">{title}</h3>
        <p className="text-muted-foreground">{description}</p>
      </div>
    </div>
  );
}

/**
 * Props for SkeletonLoader component
 */
interface SkeletonLoaderProps {
  lines?: number;
  className?: string;
}

/**
 * Skeleton loader for content placeholders
 */
export function SkeletonLoader({ lines = 3, className }: SkeletonLoaderProps) {
  return (
    <div className={cn('space-y-3', className)}>
      {Array.from({ length: lines }).map((_, index) => (
        <div
          key={index}
          className={cn(
            'h-4 bg-muted rounded animate-pulse',
            index === lines - 1 && 'w-3/4', // Last line is shorter
            index === 0 && 'w-full',
            index > 0 && index < lines - 1 && 'w-5/6'
          )}
        />
      ))}
    </div>
  );
}

/**
 * Props for MessageSkeleton component
 */
interface MessageSkeletonProps {
  count?: number;
  className?: string;
}

/**
 * Skeleton loader for message list
 */
export function MessageSkeleton({ count = 3, className }: MessageSkeletonProps) {
  return (
    <div className={cn('space-y-4', className)}>
      {Array.from({ length: count }).map((_, index) => (
        <div key={index} className="flex gap-3">
          <div className="h-8 w-8 bg-muted rounded-full animate-pulse" />
          <div className="flex-1 space-y-2">
            <div className="h-4 bg-muted rounded animate-pulse w-1/4" />
            <SkeletonLoader lines={2} />
          </div>
        </div>
      ))}
    </div>
  );
}

/**
 * Props for SessionCardSkeleton component
 */
interface SessionCardSkeletonProps {
  count?: number;
  className?: string;
}

/**
 * Skeleton loader for session cards
 */
export function SessionCardSkeleton({ count = 3, className }: SessionCardSkeletonProps) {
  return (
    <div className={cn('space-y-4', className)}>
      {Array.from({ length: count }).map((_, index) => (
        <div key={index} className="border rounded-lg p-6 space-y-4">
          <div className="flex justify-between items-start">
            <div className="space-y-2 flex-1">
              <div className="h-5 bg-muted rounded animate-pulse w-1/3" />
              <div className="h-4 bg-muted rounded animate-pulse w-1/2" />
            </div>
            <div className="h-6 w-16 bg-muted rounded animate-pulse" />
          </div>
          <div className="space-y-2">
            <div className="h-3 bg-muted rounded animate-pulse w-full" />
            <div className="h-3 bg-muted rounded animate-pulse w-3/4" />
          </div>
          <div className="flex justify-between items-center">
            <div className="h-4 bg-muted rounded animate-pulse w-1/4" />
            <div className="h-8 w-20 bg-muted rounded animate-pulse" />
          </div>
        </div>
      ))}
    </div>
  );
}
