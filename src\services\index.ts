/**
 * Service layer exports
 */

export { databaseService, DatabaseService } from './database.service';
export { aiService, AIService } from './ai.service';
export { audioService, AudioService } from './audio.service';
export { sessionService, SessionService } from './session.service';

/**
 * Service health check utility
 */
export async function checkServiceHealth() {
  const results = {
    database: false,
    ai: false,
    audio: false,
    timestamp: new Date().toISOString()
  };

  try {
    // Check database connectivity
    await databaseService.getSessionStats();
    results.database = true;
  } catch (error) {
    console.warn('Database health check failed:', error);
  }

  try {
    // Check AI service
    await aiService.validateService();
    results.ai = true;
  } catch (error) {
    console.warn('AI service health check failed:', error);
  }

  try {
    // Check audio service
    await audioService.validateService();
    results.audio = true;
  } catch (error) {
    console.warn('Audio service health check failed:', error);
  }

  return results;
}
