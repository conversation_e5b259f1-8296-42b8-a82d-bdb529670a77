/**
 * Custom hook for managing interview state and operations
 */

'use client';

import { useState, useEffect, useCallback } from 'react';
import { useChat } from 'ai/react';
import { InterviewSession, AudioStatus } from '@/types/interview';
import { errorHandler } from '@/lib/error-handler';

/**
 * Interview hook configuration
 */
interface UseInterviewConfig {
  sessionId: string;
  onError?: (error: Error) => void;
  onSessionEnd?: (session: InterviewSession) => void;
}

/**
 * Interview hook return type
 */
interface UseInterviewReturn {
  // Chat state
  messages: any[];
  input: string;
  setInput: (input: string) => void;
  handleSubmit: (e: React.FormEvent) => void;
  isLoading: boolean;
  
  // Audio state
  audioStatus: AudioStatus;
  isListening: boolean;
  
  // Interview state
  hasStarted: boolean;
  session: InterviewSession | null;
  
  // Actions
  startInterview: () => void;
  endInterview: () => Promise<void>;
  startListening: () => Promise<void>;
  stopListening: () => void;
  speakText: (text: string) => Promise<void>;
  
  // Loading states
  isLoadingSession: boolean;
  error: string | null;
}

/**
 * Custom hook for interview management
 */
export function useInterview({ sessionId, onError, onSessionEnd }: UseInterviewConfig): UseInterviewReturn {
  // Chat state
  const { messages, input, setInput, handleSubmit, isLoading } = useChat({
    api: '/api/chat',
    initialMessages: [{
      id: '1',
      role: 'assistant',
      content: 'Hello! I\'m your AI interviewer today. I\'m excited to learn about your background and technical skills. Let\'s start with a brief introduction - could you tell me about yourself and what brings you to this interview?'
    }]
  });

  // Local state
  const [audioStatus, setAudioStatus] = useState<AudioStatus>('idle');
  const [isListening, setIsListening] = useState(false);
  const [hasStarted, setHasStarted] = useState(false);
  const [session, setSession] = useState<InterviewSession | null>(null);
  const [isLoadingSession, setIsLoadingSession] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [mediaRecorder, setMediaRecorder] = useState<MediaRecorder | null>(null);

  /**
   * Load session data
   */
  const loadSession = useCallback(async () => {
    try {
      setIsLoadingSession(true);
      const response = await fetch(`/api/sessions/${sessionId}`);
      
      if (!response.ok) {
        throw new Error('Failed to load session');
      }
      
      const { session } = await response.json();
      setSession(session);
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Unknown error');
      setError(error.message);
      onError?.(error);
      errorHandler.handleClientError(error, 'Load Session');
    } finally {
      setIsLoadingSession(false);
    }
  }, [sessionId, onError]);

  /**
   * Initialize session on mount
   */
  useEffect(() => {
    loadSession();
  }, [loadSession]);

  /**
   * Start interview
   */
  const startInterview = useCallback(() => {
    setHasStarted(true);
    const initialMessage = messages[0];
    if (initialMessage && initialMessage.role === 'assistant') {
      speakText(initialMessage.content);
    }
  }, [messages]);

  /**
   * End interview
   */
  const endInterview = useCallback(async () => {
    try {
      const response = await fetch(`/api/sessions/${sessionId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          messages,
          endTime: new Date(),
          status: 'completed'
        })
      });

      if (!response.ok) {
        throw new Error('Failed to end interview');
      }

      const { session: updatedSession } = await response.json();
      setSession(updatedSession);
      onSessionEnd?.(updatedSession);
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to end interview');
      setError(error.message);
      onError?.(error);
      errorHandler.handleClientError(error, 'End Interview');
    }
  }, [sessionId, messages, onError, onSessionEnd]);

  /**
   * Start listening for audio input
   */
  const startListening = useCallback(async () => {
    try {
      setAudioStatus('listening');
      setIsListening(true);

      const stream = await navigator.mediaDevices.getUserMedia({ 
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
        } 
      });

      const recorder = new MediaRecorder(stream, {
        mimeType: 'audio/webm;codecs=opus'
      });

      const audioChunks: Blob[] = [];

      recorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunks.push(event.data);
        }
      };

      recorder.onstop = async () => {
        const audioBlob = new Blob(audioChunks, { type: 'audio/webm' });
        await processAudioInput(audioBlob);
        
        // Clean up
        stream.getTracks().forEach(track => track.stop());
      };

      recorder.start();
      setMediaRecorder(recorder);
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to start listening');
      setError(error.message);
      setAudioStatus('error');
      setIsListening(false);
      onError?.(error);
      errorHandler.handleClientError(error, 'Start Listening');
    }
  }, [onError]);

  /**
   * Stop listening
   */
  const stopListening = useCallback(() => {
    if (mediaRecorder && mediaRecorder.state === 'recording') {
      mediaRecorder.stop();
    }
    setIsListening(false);
    setAudioStatus('thinking');
  }, [mediaRecorder]);

  /**
   * Process audio input
   */
  const processAudioInput = useCallback(async (audioBlob: Blob) => {
    try {
      const formData = new FormData();
      formData.append('audio', audioBlob);

      const response = await fetch('/api/stt', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error('Speech transcription failed');
      }

      const { transcript } = await response.json();
      
      if (transcript.trim()) {
        setInput(transcript);
        // Trigger form submission
        const event = new Event('submit', { bubbles: true, cancelable: true });
        handleSubmit(event as any);
      }
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to process audio');
      setError(error.message);
      onError?.(error);
      errorHandler.handleClientError(error, 'Process Audio');
    } finally {
      setAudioStatus('idle');
    }
  }, [setInput, handleSubmit, onError]);

  /**
   * Speak text using TTS
   */
  const speakText = useCallback(async (text: string) => {
    try {
      setAudioStatus('speaking');
      
      const response = await fetch('/api/tts', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ text }),
      });
      
      if (!response.ok) {
        throw new Error('Text-to-speech failed');
      }

      const audioBlob = await response.blob();
      const audio = new Audio();
      audio.src = URL.createObjectURL(audioBlob);
      
      // Use headphones/speakers output to minimize echo
      try {
        if ('setSinkId' in audio) {
          await (audio as any).setSinkId('default');
        }
      } catch (e) {
        console.log('Audio output selection not supported');
      }
      
      audio.onended = () => {
        setAudioStatus('idle');
      };
      
      // Wait for audio to load before playing
      audio.oncanplaythrough = () => {
        audio.play();
      };
      
      // Fallback: play after a short delay
      setTimeout(() => {
        if (audio.readyState >= 2) {
          audio.play();
        }
      }, 100);
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to speak text');
      setError(error.message);
      setAudioStatus('idle');
      onError?.(error);
      errorHandler.handleClientError(error, 'Speak Text');
    }
  }, [onError]);

  /**
   * Auto-speak AI responses
   */
  useEffect(() => {
    if (!hasStarted) return;
    
    const lastMessage = messages[messages.length - 1];
    if (lastMessage && lastMessage.role === 'assistant' && !isLoading) {
      // Skip the initial message since we handle it manually
      if (messages.length > 1) {
        speakText(lastMessage.content);
      }
    }
  }, [messages, isLoading, hasStarted, speakText]);

  return {
    // Chat state
    messages,
    input,
    setInput,
    handleSubmit,
    isLoading,
    
    // Audio state
    audioStatus,
    isListening,
    
    // Interview state
    hasStarted,
    session,
    
    // Actions
    startInterview,
    endInterview,
    startListening,
    stopListening,
    speakText,
    
    // Loading states
    isLoadingSession,
    error,
  };
}
