/**
 * Custom hook for managing interview sessions
 */

'use client';

import { useState, useEffect, useCallback } from 'react';
import { InterviewSession } from '@/types/interview';
import { errorHandler } from '@/lib/error-handler';

/**
 * Sessions hook configuration
 */
interface UseSessionsConfig {
  autoLoad?: boolean;
  onError?: (error: Error) => void;
}

/**
 * Sessions hook return type
 */
interface UseSessionsReturn {
  sessions: InterviewSession[];
  isLoading: boolean;
  error: string | null;
  pagination: {
    page: number;
    totalPages: number;
    total: number;
  };
  
  // Actions
  loadSessions: (page?: number) => Promise<void>;
  createSession: () => Promise<string>;
  deleteSession: (sessionId: string) => Promise<void>;
  refreshSessions: () => Promise<void>;
}

/**
 * Custom hook for session management
 */
export function useSessions({ autoLoad = true, onError }: UseSessionsConfig = {}): UseSessionsReturn {
  const [sessions, setSessions] = useState<InterviewSession[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState({
    page: 1,
    totalPages: 1,
    total: 0,
  });

  /**
   * Load sessions from API
   */
  const loadSessions = useCallback(async (page: number = 1) => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch(`/api/sessions?page=${page}&limit=20`);
      
      if (!response.ok) {
        throw new Error('Failed to load sessions');
      }

      const data = await response.json();
      
      if (data.success === false) {
        throw new Error(data.error || 'Failed to load sessions');
      }

      setSessions(data.sessions || []);
      setPagination({
        page: data.page || 1,
        totalPages: data.totalPages || 1,
        total: data.total || 0,
      });
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Unknown error');
      setError(error.message);
      onError?.(error);
      errorHandler.handleClientError(error, 'Load Sessions');
    } finally {
      setIsLoading(false);
    }
  }, [onError]);

  /**
   * Create new session
   */
  const createSession = useCallback(async (): Promise<string> => {
    try {
      setError(null);

      const response = await fetch('/api/sessions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to create session');
      }

      const data = await response.json();
      
      if (data.success === false) {
        throw new Error(data.error || 'Failed to create session');
      }

      // Refresh sessions list
      await refreshSessions();

      return data.sessionId;
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to create session');
      setError(error.message);
      onError?.(error);
      errorHandler.handleClientError(error, 'Create Session');
      throw error;
    }
  }, [onError]);

  /**
   * Delete session
   */
  const deleteSession = useCallback(async (sessionId: string) => {
    try {
      setError(null);

      const response = await fetch(`/api/sessions/${sessionId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete session');
      }

      const data = await response.json();
      
      if (data.success === false) {
        throw new Error(data.error || 'Failed to delete session');
      }

      // Remove from local state
      setSessions(prev => prev.filter(session => session.sessionId !== sessionId));
      
      // Update pagination
      setPagination(prev => ({
        ...prev,
        total: Math.max(0, prev.total - 1),
      }));
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to delete session');
      setError(error.message);
      onError?.(error);
      errorHandler.handleClientError(error, 'Delete Session');
      throw error;
    }
  }, [onError]);

  /**
   * Refresh sessions (reload current page)
   */
  const refreshSessions = useCallback(async () => {
    await loadSessions(pagination.page);
  }, [loadSessions, pagination.page]);

  /**
   * Auto-load sessions on mount
   */
  useEffect(() => {
    if (autoLoad) {
      loadSessions();
    }
  }, [autoLoad, loadSessions]);

  return {
    sessions,
    isLoading,
    error,
    pagination,
    loadSessions,
    createSession,
    deleteSession,
    refreshSessions,
  };
}

/**
 * Hook for managing a single session
 */
export function useSession(sessionId: string) {
  const [session, setSession] = useState<InterviewSession | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadSession = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch(`/api/sessions/${sessionId}`);
      
      if (!response.ok) {
        if (response.status === 404) {
          throw new Error('Session not found');
        }
        throw new Error('Failed to load session');
      }

      const data = await response.json();
      
      if (data.success === false) {
        throw new Error(data.error || 'Failed to load session');
      }

      setSession(data.session);
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Unknown error');
      setError(error.message);
      errorHandler.handleClientError(error, 'Load Session');
    } finally {
      setIsLoading(false);
    }
  }, [sessionId]);

  const updateSession = useCallback(async (updates: Partial<InterviewSession>) => {
    try {
      setError(null);

      const response = await fetch(`/api/sessions/${sessionId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updates),
      });

      if (!response.ok) {
        throw new Error('Failed to update session');
      }

      const data = await response.json();
      
      if (data.success === false) {
        throw new Error(data.error || 'Failed to update session');
      }

      // Reload session to get updated data
      await loadSession();
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to update session');
      setError(error.message);
      errorHandler.handleClientError(error, 'Update Session');
      throw error;
    }
  }, [sessionId, loadSession]);

  useEffect(() => {
    if (sessionId) {
      loadSession();
    }
  }, [sessionId, loadSession]);

  return {
    session,
    isLoading,
    error,
    loadSession,
    updateSession,
  };
}
