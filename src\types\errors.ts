/**
 * Custom error types for the AI Interviewer application
 */

/**
 * Base error class for application-specific errors
 */
export class AppError extends Error {
  public readonly statusCode: number;
  public readonly isOperational: boolean;
  public readonly details?: string;

  constructor(
    message: string,
    statusCode: number = 500,
    isOperational: boolean = true,
    details?: string
  ) {
    super(message);
    this.name = this.constructor.name;
    this.statusCode = statusCode;
    this.isOperational = isOperational;
    this.details = details;

    // Maintains proper stack trace for where our error was thrown
    Error.captureStackTrace(this, this.constructor);
  }
}

/**
 * Database operation errors
 */
export class DatabaseError extends AppError {
  constructor(message: string, details?: string) {
    super(message, 500, true, details);
  }
}

/**
 * API validation errors
 */
export class ValidationError extends AppError {
  constructor(message: string, details?: string) {
    super(message, 400, true, details);
  }
}

/**
 * Authentication/Authorization errors
 */
export class AuthError extends AppError {
  constructor(message: string, details?: string) {
    super(message, 401, true, details);
  }
}

/**
 * External service errors (Deepgram, Google AI, etc.)
 */
export class ExternalServiceError extends AppError {
  public readonly service: string;

  constructor(service: string, message: string, details?: string) {
    super(`${service} Error: ${message}`, 502, true, details);
    this.service = service;
  }
}

/**
 * Session-related errors
 */
export class SessionError extends AppError {
  constructor(message: string, details?: string) {
    super(message, 404, true, details);
  }
}

/**
 * Audio processing errors
 */
export class AudioError extends AppError {
  constructor(message: string, details?: string) {
    super(message, 422, true, details);
  }
}

/**
 * Error response format for API endpoints
 */
export interface ErrorResponse {
  success: false;
  error: string;
  details?: string;
  statusCode: number;
  timestamp: string;
  path?: string;
}

/**
 * Error severity levels
 */
export enum ErrorSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

/**
 * Error logging context
 */
export interface ErrorContext {
  userId?: string;
  sessionId?: string;
  action?: string;
  metadata?: Record<string, any>;
  severity?: ErrorSeverity;
}

/**
 * Type guard to check if an error is an AppError
 */
export function isAppError(error: any): error is AppError {
  return error instanceof AppError;
}

/**
 * Type guard to check if an error is operational
 */
export function isOperationalError(error: any): boolean {
  if (isAppError(error)) {
    return error.isOperational;
  }
  return false;
}
