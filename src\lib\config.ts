/**
 * Application configuration
 */

import { validateEnvironment } from './validation';

/**
 * Environment configuration
 */
export const config = {
  // Database
  mongodb: {
    uri: process.env.MONGODB_URI || 'mongodb://localhost:27017/ai-interviewer',
  },
  
  // AI Services
  ai: {
    googleApiKey: process.env.GOOGLE_GENERATIVE_AI_API_KEY,
  },
  
  // Audio Services
  audio: {
    deepgramApiKey: process.env.DEEPGRAM_API_KEY,
    deepgramPublicApiKey: process.env.NEXT_PUBLIC_DEEPGRAM_API_KEY,
  },
  
  // Application
  app: {
    nodeEnv: process.env.NODE_ENV || 'development',
    port: process.env.PORT || 3000,
    baseUrl: process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000',
  },
  
  // Feature flags
  features: {
    enableAudioRecording: process.env.ENABLE_AUDIO_RECORDING === 'true',
    enableAnalytics: process.env.ENABLE_ANALYTICS === 'true',
    enableDebugMode: process.env.ENABLE_DEBUG_MODE === 'true',
  },
} as const;

/**
 * Validate configuration on startup
 */
export function validateConfig(): void {
  try {
    validateEnvironment();
  } catch (error) {
    console.error('Configuration validation failed:', error);
    if (config.app.nodeEnv === 'production') {
      process.exit(1);
    }
  }
}

/**
 * Get configuration value with fallback
 */
export function getConfigValue<T>(
  path: string,
  fallback: T
): T {
  const keys = path.split('.');
  let value: any = config;
  
  for (const key of keys) {
    value = value?.[key];
    if (value === undefined) {
      return fallback;
    }
  }
  
  return value as T;
}

/**
 * Check if running in development mode
 */
export const isDevelopment = config.app.nodeEnv === 'development';

/**
 * Check if running in production mode
 */
export const isProduction = config.app.nodeEnv === 'production';

/**
 * Check if running in test mode
 */
export const isTest = config.app.nodeEnv === 'test';
