/**
 * Validation utilities and schemas for the AI Interviewer application
 */

import { ValidationError } from '@/types/errors';
import { Message, InterviewSession, TTSOptions } from '@/types/interview';

/**
 * Validation result type
 */
export interface ValidationResult<T = any> {
  isValid: boolean;
  data?: T;
  errors: string[];
}

/**
 * Validates a message object
 */
export function validateMessage(data: any): ValidationResult<Message> {
  const errors: string[] = [];

  if (!data || typeof data !== 'object') {
    errors.push('Message must be an object');
    return { isValid: false, errors };
  }

  if (!data.id || typeof data.id !== 'string') {
    errors.push('Message ID is required and must be a string');
  }

  if (!data.role || !['user', 'assistant'].includes(data.role)) {
    errors.push('Message role must be either "user" or "assistant"');
  }

  if (!data.content || typeof data.content !== 'string') {
    errors.push('Message content is required and must be a string');
  } else if (data.content.length > 10000) {
    errors.push('Message content cannot exceed 10,000 characters');
  }

  if (data.timestamp && !(data.timestamp instanceof Date) && isNaN(Date.parse(data.timestamp))) {
    errors.push('Message timestamp must be a valid date');
  }

  if (data.metadata && typeof data.metadata !== 'object') {
    errors.push('Message metadata must be an object');
  }

  return {
    isValid: errors.length === 0,
    data: errors.length === 0 ? data as Message : undefined,
    errors
  };
}

/**
 * Validates an interview session object
 */
export function validateInterviewSession(data: any): ValidationResult<InterviewSession> {
  const errors: string[] = [];

  if (!data || typeof data !== 'object') {
    errors.push('Interview session must be an object');
    return { isValid: false, errors };
  }

  if (!data.sessionId || typeof data.sessionId !== 'string') {
    errors.push('Session ID is required and must be a string');
  }

  if (!Array.isArray(data.messages)) {
    errors.push('Messages must be an array');
  } else {
    data.messages.forEach((message: any, index: number) => {
      const messageValidation = validateMessage(message);
      if (!messageValidation.isValid) {
        errors.push(`Message at index ${index}: ${messageValidation.errors.join(', ')}`);
      }
    });
  }

  if (!data.startTime || (!(data.startTime instanceof Date) && isNaN(Date.parse(data.startTime)))) {
    errors.push('Start time is required and must be a valid date');
  }

  if (data.endTime && !(data.endTime instanceof Date) && isNaN(Date.parse(data.endTime))) {
    errors.push('End time must be a valid date');
  }

  if (!data.status || !['active', 'completed', 'paused', 'cancelled'].includes(data.status)) {
    errors.push('Status must be one of: active, completed, paused, cancelled');
  }

  if (typeof data.audioRecorded !== 'boolean') {
    errors.push('Audio recorded flag must be a boolean');
  }

  return {
    isValid: errors.length === 0,
    data: errors.length === 0 ? data as InterviewSession : undefined,
    errors
  };
}

/**
 * Validates TTS options
 */
export function validateTTSOptions(data: any): ValidationResult<TTSOptions> {
  const errors: string[] = [];

  if (!data || typeof data !== 'object') {
    errors.push('TTS options must be an object');
    return { isValid: false, errors };
  }

  if (!data.text || typeof data.text !== 'string') {
    errors.push('Text is required and must be a string');
  } else if (data.text.length > 5000) {
    errors.push('Text cannot exceed 5,000 characters');
  }

  if (data.model && typeof data.model !== 'string') {
    errors.push('Model must be a string');
  }

  if (data.rate && (typeof data.rate !== 'number' || data.rate < 0.5 || data.rate > 2.0)) {
    errors.push('Rate must be a number between 0.5 and 2.0');
  }

  if (data.pitch && (typeof data.pitch !== 'number' || data.pitch < -20 || data.pitch > 20)) {
    errors.push('Pitch must be a number between -20 and 20');
  }

  return {
    isValid: errors.length === 0,
    data: errors.length === 0 ? data as TTSOptions : undefined,
    errors
  };
}

/**
 * Validates session ID format
 */
export function validateSessionId(sessionId: string): boolean {
  if (!sessionId || typeof sessionId !== 'string') {
    return false;
  }
  
  // UUID v4 format validation
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(sessionId);
}

/**
 * Sanitizes text input to prevent XSS and other attacks
 */
export function sanitizeText(text: string): string {
  if (typeof text !== 'string') {
    return '';
  }
  
  return text
    .replace(/[<>]/g, '') // Remove potential HTML tags
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/on\w+=/gi, '') // Remove event handlers
    .trim();
}

/**
 * Validates and throws ValidationError if invalid
 */
export function validateOrThrow<T>(
  data: any,
  validator: (data: any) => ValidationResult<T>,
  context?: string
): T {
  const result = validator(data);
  if (!result.isValid) {
    const contextMsg = context ? `${context}: ` : '';
    throw new ValidationError(`${contextMsg}${result.errors.join(', ')}`);
  }
  return result.data!;
}

/**
 * Validates environment variables
 */
export function validateEnvironment(): void {
  const required = [
    'MONGODB_URI',
    'GOOGLE_GENERATIVE_AI_API_KEY',
    'DEEPGRAM_API_KEY'
  ];

  const missing = required.filter(key => !process.env[key]);
  
  if (missing.length > 0) {
    throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
  }
}
