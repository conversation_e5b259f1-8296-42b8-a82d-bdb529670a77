/**
 * Database service for MongoDB operations
 */

import { MongoClient, Db, Collection } from 'mongodb';
import { InterviewSession, Message } from '@/types/interview';
import { DatabaseError } from '@/types/errors';
import { validateInterviewSession, validateSessionId } from '@/lib/validation';
import clientPromise from '@/lib/mongodb';

/**
 * Database service class for handling MongoDB operations
 */
export class DatabaseService {
  private client: MongoClient | null = null;
  private db: Db | null = null;

  /**
   * Initialize database connection
   */
  private async initialize(): Promise<void> {
    if (!this.client) {
      try {
        this.client = await clientPromise;
        this.db = this.client.db('ai-interviewer');
      } catch (error) {
        throw new DatabaseError(
          'Failed to connect to database',
          error instanceof Error ? error.message : String(error)
        );
      }
    }
  }

  /**
   * Get sessions collection
   */
  private async getSessionsCollection(): Promise<Collection<InterviewSession>> {
    await this.initialize();
    if (!this.db) {
      throw new DatabaseError('Database not initialized');
    }
    return this.db.collection<InterviewSession>('sessions');
  }

  /**
   * Create a new interview session
   */
  async createSession(sessionData: Omit<InterviewSession, '_id'>): Promise<InterviewSession> {
    try {
      const validation = validateInterviewSession(sessionData);
      if (!validation.isValid) {
        throw new DatabaseError(`Invalid session data: ${validation.errors.join(', ')}`);
      }

      const collection = await this.getSessionsCollection();
      const result = await collection.insertOne(sessionData);
      
      if (!result.insertedId) {
        throw new DatabaseError('Failed to create session');
      }

      return {
        ...sessionData,
        _id: result.insertedId.toString()
      };
    } catch (error) {
      if (error instanceof DatabaseError) {
        throw error;
      }
      throw new DatabaseError(
        'Failed to create session',
        error instanceof Error ? error.message : String(error)
      );
    }
  }

  /**
   * Get session by ID
   */
  async getSession(sessionId: string): Promise<InterviewSession | null> {
    try {
      if (!validateSessionId(sessionId)) {
        throw new DatabaseError('Invalid session ID format');
      }

      const collection = await this.getSessionsCollection();
      const session = await collection.findOne({ sessionId });
      
      return session;
    } catch (error) {
      if (error instanceof DatabaseError) {
        throw error;
      }
      throw new DatabaseError(
        'Failed to retrieve session',
        error instanceof Error ? error.message : String(error)
      );
    }
  }

  /**
   * Update session
   */
  async updateSession(
    sessionId: string, 
    updates: Partial<Omit<InterviewSession, '_id' | 'sessionId'>>
  ): Promise<boolean> {
    try {
      if (!validateSessionId(sessionId)) {
        throw new DatabaseError('Invalid session ID format');
      }

      const collection = await this.getSessionsCollection();
      const result = await collection.updateOne(
        { sessionId },
        { 
          $set: {
            ...updates,
            updatedAt: new Date()
          }
        }
      );

      return result.matchedCount > 0;
    } catch (error) {
      if (error instanceof DatabaseError) {
        throw error;
      }
      throw new DatabaseError(
        'Failed to update session',
        error instanceof Error ? error.message : String(error)
      );
    }
  }

  /**
   * Add message to session
   */
  async addMessageToSession(sessionId: string, message: Message): Promise<boolean> {
    try {
      if (!validateSessionId(sessionId)) {
        throw new DatabaseError('Invalid session ID format');
      }

      const collection = await this.getSessionsCollection();
      const result = await collection.updateOne(
        { sessionId },
        { 
          $push: { messages: message },
          $set: { updatedAt: new Date() }
        }
      );

      return result.matchedCount > 0;
    } catch (error) {
      if (error instanceof DatabaseError) {
        throw error;
      }
      throw new DatabaseError(
        'Failed to add message to session',
        error instanceof Error ? error.message : String(error)
      );
    }
  }

  /**
   * Get all sessions with pagination
   */
  async getSessions(
    page: number = 1, 
    limit: number = 20,
    sortBy: 'startTime' | 'endTime' | 'status' = 'startTime',
    sortOrder: 'asc' | 'desc' = 'desc'
  ): Promise<{ sessions: InterviewSession[]; total: number; page: number; totalPages: number }> {
    try {
      const collection = await this.getSessionsCollection();
      const skip = (page - 1) * limit;
      const sort = { [sortBy]: sortOrder === 'desc' ? -1 : 1 };

      const [sessions, total] = await Promise.all([
        collection.find({}).sort(sort).skip(skip).limit(limit).toArray(),
        collection.countDocuments({})
      ]);

      return {
        sessions,
        total,
        page,
        totalPages: Math.ceil(total / limit)
      };
    } catch (error) {
      throw new DatabaseError(
        'Failed to retrieve sessions',
        error instanceof Error ? error.message : String(error)
      );
    }
  }

  /**
   * Delete session
   */
  async deleteSession(sessionId: string): Promise<boolean> {
    try {
      if (!validateSessionId(sessionId)) {
        throw new DatabaseError('Invalid session ID format');
      }

      const collection = await this.getSessionsCollection();
      const result = await collection.deleteOne({ sessionId });

      return result.deletedCount > 0;
    } catch (error) {
      if (error instanceof DatabaseError) {
        throw error;
      }
      throw new DatabaseError(
        'Failed to delete session',
        error instanceof Error ? error.message : String(error)
      );
    }
  }

  /**
   * Get session statistics
   */
  async getSessionStats(): Promise<{
    totalSessions: number;
    activeSessions: number;
    completedSessions: number;
    averageDuration: number;
  }> {
    try {
      const collection = await this.getSessionsCollection();
      
      const stats = await collection.aggregate([
        {
          $group: {
            _id: null,
            totalSessions: { $sum: 1 },
            activeSessions: {
              $sum: { $cond: [{ $eq: ['$status', 'active'] }, 1, 0] }
            },
            completedSessions: {
              $sum: { $cond: [{ $eq: ['$status', 'completed'] }, 1, 0] }
            },
            averageDuration: {
              $avg: {
                $cond: [
                  { $and: ['$endTime', '$startTime'] },
                  { $subtract: ['$endTime', '$startTime'] },
                  null
                ]
              }
            }
          }
        }
      ]).toArray();

      const result = stats[0] || {
        totalSessions: 0,
        activeSessions: 0,
        completedSessions: 0,
        averageDuration: 0
      };

      return {
        ...result,
        averageDuration: result.averageDuration ? Math.round(result.averageDuration / 1000) : 0 // Convert to seconds
      };
    } catch (error) {
      throw new DatabaseError(
        'Failed to retrieve session statistics',
        error instanceof Error ? error.message : String(error)
      );
    }
  }
}

// Export singleton instance
export const databaseService = new DatabaseService();
