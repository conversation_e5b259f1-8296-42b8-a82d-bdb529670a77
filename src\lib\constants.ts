/**
 * Application constants
 */

/**
 * API endpoints
 */
export const API_ENDPOINTS = {
  SESSIONS: '/api/sessions',
  CHAT: '/api/chat',
  STT: '/api/stt',
  TTS: '/api/tts',
} as const;

/**
 * Audio configuration
 */
export const AUDIO_CONFIG = {
  STT: {
    MODEL: 'nova-2',
    LANGUAGE: 'en-US',
    SMART_FORMAT: true,
    PUNCTUATE: true,
  },
  TTS: {
    MODEL: 'aura-asteria-en',
    ENCODING: 'linear16',
    SAMPLE_RATE: 24000,
  },
  RECORDING: {
    MIME_TYPE: 'audio/webm;codecs=opus',
    TIME_SLICE: 100, // ms
  },
} as const;

/**
 * AI configuration
 */
export const AI_CONFIG = {
  MODEL: 'gemini-2.0-flash-exp',
  MAX_TOKENS: 1000,
  TEMPERATURE: 0.7,
  TOP_P: 0.9,
  TOP_K: 40,
} as const;

/**
 * Database configuration
 */
export const DB_CONFIG = {
  NAME: 'ai-interviewer',
  COLLECTIONS: {
    SESSIONS: 'sessions',
  },
} as const;

/**
 * Pagination defaults
 */
export const PAGINATION = {
  DEFAULT_PAGE: 1,
  DEFAULT_LIMIT: 20,
  MAX_LIMIT: 100,
} as const;

/**
 * Validation limits
 */
export const VALIDATION_LIMITS = {
  MESSAGE_CONTENT_MAX_LENGTH: 10000,
  TTS_TEXT_MAX_LENGTH: 5000,
  SESSION_ID_LENGTH: 36, // UUID v4 length
} as const;

/**
 * Error messages
 */
export const ERROR_MESSAGES = {
  INVALID_SESSION_ID: 'Invalid session ID format',
  SESSION_NOT_FOUND: 'Session not found',
  AUDIO_FORMAT_INVALID: 'Invalid audio format. Please use WAV, MP3, or FLAC format.',
  NO_AUDIO_DATA: 'No audio data provided',
  MESSAGES_REQUIRED: 'Messages must be an array',
  API_KEY_MISSING: 'API key not configured',
  MICROPHONE_ACCESS_DENIED: 'Microphone access denied',
  SPEECH_RECOGNITION_FAILED: 'Speech recognition failed',
  TTS_GENERATION_FAILED: 'Text-to-speech generation failed',
} as const;

/**
 * Success messages
 */
export const SUCCESS_MESSAGES = {
  SESSION_CREATED: 'Interview session created successfully',
  SESSION_UPDATED: 'Session updated successfully',
  SESSION_DELETED: 'Session deleted successfully',
  INTERVIEW_COMPLETED: 'Interview completed successfully',
} as const;

/**
 * UI constants
 */
export const UI_CONFIG = {
  DEBOUNCE_DELAY: 300, // ms
  ANIMATION_DURATION: 200, // ms
  TOAST_DURATION: 5000, // ms
  MAX_MESSAGE_HEIGHT: 384, // px (max-h-96)
  AUDIO_BUTTON_SIZE: 64, // px
} as const;

/**
 * Local storage keys
 */
export const STORAGE_KEYS = {
  AUDIO_SETTINGS: 'ai-interviewer-audio-settings',
  USER_PREFERENCES: 'ai-interviewer-user-preferences',
  LAST_SESSION: 'ai-interviewer-last-session',
} as const;

/**
 * Interview configuration defaults
 */
export const INTERVIEW_DEFAULTS = {
  MAX_DURATION: 60, // minutes
  ENABLE_AUDIO_RECORDING: false,
  DIFFICULTY: 'intermediate' as const,
  LANGUAGE: 'en-US',
  FOCUS_AREAS: ['general software engineering'],
} as const;
