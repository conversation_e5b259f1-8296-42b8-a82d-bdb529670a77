/**
 * Audio status indicator component
 */

import React from 'react';
import { cn } from '@/lib/utils';
import { AudioStatus } from '@/types/interview';
import { Mic, MicOff, Volume2, Loader2, AlertCircle } from 'lucide-react';

/**
 * Props for AudioStatusIndicator component
 */
interface AudioStatusIndicatorProps {
  status: AudioStatus;
  className?: string;
  showText?: boolean;
  size?: 'sm' | 'md' | 'lg';
}

/**
 * Audio status indicator component
 */
export function AudioStatusIndicator({ 
  status, 
  className, 
  showText = true,
  size = 'md' 
}: AudioStatusIndicatorProps) {
  const getStatusConfig = () => {
    switch (status) {
      case 'listening':
        return {
          icon: Mic,
          text: 'Listening...',
          color: 'text-green-600',
          bgColor: 'bg-green-100',
          animate: 'animate-pulse'
        };
      case 'thinking':
        return {
          icon: Loader2,
          text: 'Processing...',
          color: 'text-blue-600',
          bgColor: 'bg-blue-100',
          animate: 'animate-spin'
        };
      case 'speaking':
        return {
          icon: Volume2,
          text: 'Speaking...',
          color: 'text-purple-600',
          bgColor: 'bg-purple-100',
          animate: 'animate-pulse'
        };
      case 'error':
        return {
          icon: AlertCircle,
          text: 'Error',
          color: 'text-red-600',
          bgColor: 'bg-red-100',
          animate: ''
        };
      default:
        return {
          icon: MicOff,
          text: 'Ready',
          color: 'text-gray-600',
          bgColor: 'bg-gray-100',
          animate: ''
        };
    }
  };

  const config = getStatusConfig();
  const Icon = config.icon;

  const sizeClasses = {
    sm: {
      icon: 'h-4 w-4',
      container: 'px-2 py-1 text-xs',
      gap: 'gap-1'
    },
    md: {
      icon: 'h-5 w-5',
      container: 'px-3 py-2 text-sm',
      gap: 'gap-2'
    },
    lg: {
      icon: 'h-6 w-6',
      container: 'px-4 py-3 text-base',
      gap: 'gap-3'
    }
  };

  const sizeConfig = sizeClasses[size];

  return (
    <div className={cn(
      'inline-flex items-center rounded-full border',
      sizeConfig.container,
      sizeConfig.gap,
      config.color,
      config.bgColor,
      'border-current/20',
      className
    )}>
      <Icon className={cn(
        sizeConfig.icon,
        config.animate
      )} />
      {showText && (
        <span className="font-medium">
          {config.text}
        </span>
      )}
    </div>
  );
}

/**
 * Props for AudioWaveform component
 */
interface AudioWaveformProps {
  isActive: boolean;
  className?: string;
  barCount?: number;
}

/**
 * Animated audio waveform component
 */
export function AudioWaveform({ 
  isActive, 
  className, 
  barCount = 5 
}: AudioWaveformProps) {
  return (
    <div className={cn(
      'flex items-center justify-center gap-1',
      className
    )}>
      {Array.from({ length: barCount }).map((_, index) => (
        <div
          key={index}
          className={cn(
            'w-1 bg-current rounded-full transition-all duration-300',
            isActive ? 'animate-pulse' : 'opacity-30',
            // Varying heights for visual effect
            index % 2 === 0 ? 'h-4' : 'h-6',
            index === Math.floor(barCount / 2) ? 'h-8' : ''
          )}
          style={{
            animationDelay: `${index * 0.1}s`,
            animationDuration: `${0.5 + (index * 0.1)}s`
          }}
        />
      ))}
    </div>
  );
}

/**
 * Props for VoiceActivityIndicator component
 */
interface VoiceActivityIndicatorProps {
  isActive: boolean;
  level?: number; // 0-100
  className?: string;
}

/**
 * Voice activity level indicator
 */
export function VoiceActivityIndicator({ 
  isActive, 
  level = 0, 
  className 
}: VoiceActivityIndicatorProps) {
  const normalizedLevel = Math.max(0, Math.min(100, level));
  
  return (
    <div className={cn(
      'flex items-center gap-2',
      className
    )}>
      <Mic className={cn(
        'h-4 w-4',
        isActive ? 'text-green-600' : 'text-gray-400'
      )} />
      <div className="flex-1 h-2 bg-gray-200 rounded-full overflow-hidden">
        <div 
          className={cn(
            'h-full transition-all duration-150 rounded-full',
            isActive ? 'bg-green-500' : 'bg-gray-400'
          )}
          style={{ width: `${normalizedLevel}%` }}
        />
      </div>
      <span className="text-xs text-gray-500 min-w-[3ch]">
        {Math.round(normalizedLevel)}%
      </span>
    </div>
  );
}

/**
 * Props for AudioControls component
 */
interface AudioControlsProps {
  status: AudioStatus;
  isListening: boolean;
  onStartListening: () => void;
  onStopListening: () => void;
  disabled?: boolean;
  className?: string;
}

/**
 * Audio control buttons component
 */
export function AudioControls({
  status,
  isListening,
  onStartListening,
  onStopListening,
  disabled = false,
  className
}: AudioControlsProps) {
  const isDisabled = disabled || status === 'thinking' || status === 'speaking';

  return (
    <div className={cn(
      'flex items-center gap-4',
      className
    )}>
      <AudioStatusIndicator 
        status={status} 
        size="md"
        className="flex-shrink-0"
      />
      
      <button
        onClick={isListening ? onStopListening : onStartListening}
        disabled={isDisabled}
        className={cn(
          'flex items-center justify-center h-16 w-16 rounded-full transition-all',
          'focus:outline-none focus:ring-2 focus:ring-offset-2',
          isListening 
            ? 'bg-red-500 hover:bg-red-600 focus:ring-red-500 text-white' 
            : 'bg-blue-500 hover:bg-blue-600 focus:ring-blue-500 text-white',
          isDisabled && 'opacity-50 cursor-not-allowed'
        )}
      >
        {isListening ? (
          <MicOff className="h-6 w-6" />
        ) : (
          <Mic className="h-6 w-6" />
        )}
      </button>
      
      {isListening && (
        <AudioWaveform 
          isActive={status === 'listening'} 
          className="text-blue-500"
        />
      )}
    </div>
  );
}
