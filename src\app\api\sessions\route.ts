import { NextRequest, NextResponse } from 'next/server';
import { sessionService } from '@/services';
import { withApiErrorHandling } from '@/lib/error-handler';
import { ApiResponse } from '@/types/interview';

/**
 * Create a new interview session
 */
export const POST = withApiErrorHandling(
  async () => {
    const session = await sessionService.createSession();

    const response: ApiResponse<{ sessionId: string }> = {
      success: true,
      data: { sessionId: session.sessionId },
    };

    return NextResponse.json(response);
  },
  { action: 'Create Session' }
);

/**
 * Get all interview sessions with pagination
 */
export const GET = withApiErrorHandling(
  async (req: NextRequest) => {
    const { searchParams } = new URL(req.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');

    const result = await sessionService.getSessions(page, limit);

    const response: ApiResponse<typeof result> = {
      success: true,
      data: result,
    };

    return NextResponse.json(response);
  },
  { action: 'Get Sessions' }
);
