import { NextRequest, NextResponse } from 'next/server';
import clientPromise from '@/lib/mongodb';
import { InterviewSession } from '@/types/interview';

export async function POST() {
  try {
    const client = await clientPromise;
    const db = client.db('ai-interviewer');
    
    const sessionId = crypto.randomUUID();
    const session: InterviewSession = {
      sessionId,
      messages: [],
      startTime: new Date(),
      status: 'active',
      audioRecorded: false,
    };
    
    await db.collection('sessions').insertOne(session);
    
    return NextResponse.json({ sessionId });
  } catch (error) {
    return NextResponse.json({ error: 'Failed to create session' }, { status: 500 });
  }
}

export async function GET() {
  try {
    const client = await clientPromise;
    const db = client.db('ai-interviewer');
    
    const sessions = await db.collection('sessions')
      .find({})
      .sort({ startTime: -1 })
      .toArray();
    
    return NextResponse.json({ sessions });
  } catch (error) {
    return NextResponse.json({ error: 'Failed to fetch sessions' }, { status: 500 });
  }
}