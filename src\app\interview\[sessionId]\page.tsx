'use client';

import React from 'react';
import { useParams, useRouter } from 'next/navigation';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { LoadingCard, LoadingOverlay } from '@/components/ui/loading';
import { AudioControls } from '@/components/ui/audio-status';
import { MessageList, MessageInput } from '@/components/ui/message';
import { ErrorBoundary } from '@/components/error-boundary';
import { useInterview } from '@/hooks/use-interview';
import { Square, ArrowLeft } from 'lucide-react';

/**
 * Interview page component
 */
function InterviewPageContent() {
  const params = useParams();
  const router = useRouter();
  const sessionId = params.sessionId as string;

  const {
    // Chat state
    messages,
    input,
    setInput,
    handleSubmit,
    isLoading,

    // Audio state
    audioStatus,
    isListening,

    // Interview state
    hasStarted,
    session,

    // Actions
    startInterview,
    endInterview,
    startListening,
    stopListening,

    // Loading states
    isLoadingSession,
    error,
  } = useInterview({
    sessionId,
    onError: (error) => {
      console.error('Interview error:', error);
    },
    onSessionEnd: (session) => {
      router.push(`/history/${session.sessionId}`);
    },
  });

  // Handle loading states
  if (isLoadingSession) {
    return (
      <div className='min-h-screen bg-gray-50 p-4'>
        <LoadingCard
          title='Loading Interview Session'
          description='Please wait while we prepare your interview...'
        />
      </div>
    );
  }

  // Handle error states
  if (error) {
    return (
      <div className='min-h-screen bg-gray-50 p-4 flex items-center justify-center'>
        <Card className='w-full max-w-md'>
          <CardContent className='pt-6 text-center'>
            <p className='text-red-600 mb-4'>{error}</p>
            <Button onClick={() => router.push('/')}>Return Home</Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="flex items-center mb-6">
          <Button
            variant="ghost"
            onClick={() => router.push('/')}
            className="mr-4"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Home
          </Button>
          <h1 className="text-2xl font-bold">AI Interview Session</h1>
          {session && (
            <div className="ml-auto text-sm text-muted-foreground">
              Session: {session.sessionId.slice(0, 8)}...
            </div>
          )}
        </div>

        {/* Messages */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Interview Conversation</CardTitle>
          </CardHeader>
          <CardContent className="p-0">
            <div className="max-h-96 overflow-y-auto">
              <MessageList
                messages={messages}
                isLoading={isLoading}
                showTimestamps={true}
              />
            </div>
          </CardContent>
        </Card>

        {/* Controls */}
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-4">
                {hasStarted ? (
                  <AudioControls
                    status={audioStatus}
                    isListening={isListening}
                    onStartListening={startListening}
                    onStopListening={stopListening}
                  />
                ) : (
                  <Button
                    size="lg"
                    onClick={startInterview}
                    className="h-12 px-6"
                  >
                    Start Interview
                  </Button>
                )}
              </div>

              <Button
                variant="destructive"
                onClick={endInterview}
                disabled={!hasStarted}
              >
                <Square className="w-4 h-4 mr-2" />
                End Interview
              </Button>
            </div>

            {hasStarted && (
              <MessageInput
                value={input}
                onChange={setInput}
                onSubmit={handleSubmit}
                disabled={isLoading || audioStatus === 'speaking'}
                placeholder="Type your response or use voice input..."
              />
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

/**
 * Main interview page with error boundary
 */
export default function InterviewPage() {
  return (
    <ErrorBoundary>
      <InterviewPageContent />
    </ErrorBoundary>
  );

