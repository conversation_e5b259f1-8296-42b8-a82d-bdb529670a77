/**
 * Represents a message in an interview conversation
 */
export interface Message {
  /** Unique identifier for the message */
  id: string;
  /** Role of the message sender */
  role: 'user' | 'assistant';
  /** Content of the message */
  content: string;
  /** Timestamp when the message was created */
  timestamp: Date;
  /** Optional metadata for the message */
  metadata?: {
    /** Duration of audio if this was a voice message */
    audioDuration?: number;
    /** Confidence score for speech-to-text transcription */
    transcriptionConfidence?: number;
    /** Whether this message was generated by AI */
    isAiGenerated?: boolean;
  };
}

/**
 * Status of an interview session
 */
export type InterviewStatus = 'active' | 'completed' | 'paused' | 'cancelled';

/**
 * Represents an interview session
 */
export interface InterviewSession {
  /** MongoDB document ID */
  _id?: string;
  /** Unique session identifier */
  sessionId: string;
  /** Array of messages in the conversation */
  messages: Message[];
  /** When the interview started */
  startTime: Date;
  /** When the interview ended (if completed) */
  endTime?: Date;
  /** Current status of the interview */
  status: InterviewStatus;
  /** Whether audio was recorded during the session */
  audioRecorded: boolean;
  /** Optional session metadata */
  metadata?: {
    /** Total duration of the interview in seconds */
    duration?: number;
    /** Number of questions asked */
    questionCount?: number;
    /** Average response time in seconds */
    averageResponseTime?: number;
    /** Interview type or category */
    interviewType?: string;
  };
}

/**
 * API Response wrapper for consistent error handling
 */
export interface ApiResponse<T = any> {
  /** Whether the request was successful */
  success: boolean;
  /** Response data if successful */
  data?: T;
  /** Error message if unsuccessful */
  error?: string;
  /** Additional error details */
  details?: string;
  /** HTTP status code */
  statusCode?: number;
}

/**
 * Speech-to-text transcription result
 */
export interface TranscriptionResult {
  /** Transcribed text */
  transcript: string;
  /** Confidence score (0-1) */
  confidence?: number;
  /** Duration of the audio in seconds */
  duration?: number;
  /** Language detected */
  language?: string;
}

/**
 * Text-to-speech generation options
 */
export interface TTSOptions {
  /** Text to convert to speech */
  text: string;
  /** Voice model to use */
  model?: string;
  /** Speech rate (0.5-2.0) */
  rate?: number;
  /** Voice pitch (-20 to 20) */
  pitch?: number;
}

/**
 * Audio recording status
 */
export type AudioStatus =
  | 'idle'
  | 'listening'
  | 'thinking'
  | 'speaking'
  | 'error';

/**
 * Interview configuration options
 */
export interface InterviewConfig {
  /** Maximum duration in minutes */
  maxDuration?: number;
  /** Whether to enable audio recording */
  enableAudioRecording?: boolean;
  /** Interview difficulty level */
  difficulty?: 'beginner' | 'intermediate' | 'advanced';
  /** Focus areas for the interview */
  focusAreas?: string[];
  /** Language preference */
  language?: string;
}
