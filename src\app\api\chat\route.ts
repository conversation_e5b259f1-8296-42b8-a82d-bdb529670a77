import { NextRequest } from 'next/server';
import { aiService } from '@/services';
import { withApiErrorHandling } from '@/lib/error-handler';
import { validateMessage } from '@/lib/validation';

export const maxDuration = 30;

/**
 * Handle chat messages and generate AI responses
 */
export const POST = withApiErrorHandling(
  async (req: NextRequest) => {
    const { messages, sessionId } = await req.json();

    // Validate messages
    if (!Array.isArray(messages)) {
      throw new Error('Messages must be an array');
    }

    // Validate each message
    messages.forEach((message, index) => {
      const validation = validateMessage(message);
      if (!validation.isValid) {
        throw new Error(
          `Invalid message at index ${index}: ${validation.errors.join(', ')}`
        );
      }
    });

    // Generate context if sessionId is provided
    const context = sessionId
      ? {
          sessionId,
          messageCount: messages.length,
          duration: undefined, // Could be calculated if needed
        }
      : undefined;

    // Generate AI response stream
    const responseStream = await aiService.generateResponse(messages, context);

    // Return the stream response
    return new Response(responseStream, {
      headers: {
        'Content-Type': 'text/plain; charset=utf-8',
        'Transfer-Encoding': 'chunked',
      },
    });
  },
  { action: 'Generate Chat Response' }
);
