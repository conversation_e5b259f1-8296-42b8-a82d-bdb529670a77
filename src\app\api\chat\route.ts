import { google } from '@ai-sdk/google';
import { convertToCoreMessages, streamText } from 'ai';
import { NextRequest } from 'next/server';

export const maxDuration = 30;

export async function POST(req: NextRequest) {
  const { messages } = await req.json();

  const result = await streamText({
    model: google('gemini-2.0-flash-exp'),
    system: `You are a friendly but professional senior software engineer conducting an interview.
             Start the conversation with a brief introduction.
             Ask a mix of technical and behavioral questions.
             Keep your responses concise and conversational.
             Dynamically ask follow-up questions based on the user's answers.
             Do not repeat questions.
             Focus on understanding the candidate's problem-solving approach and technical knowledge.`,
    messages: convertToCoreMessages(messages),
  });

  return result.toDataStreamResponse();
}