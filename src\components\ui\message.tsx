/**
 * Message components for chat interface
 */

import React from 'react';
import { cn } from '@/lib/utils';
import { Message } from '@/types/interview';
import { User, Bot, Play, Volume2 } from 'lucide-react';
import { Button } from './button';

/**
 * Props for MessageBubble component
 */
interface MessageBubbleProps {
  message: Message;
  onPlayAudio?: (messageId: string) => void;
  showTimestamp?: boolean;
  showPlayButton?: boolean;
  className?: string;
}

/**
 * Individual message bubble component
 */
export function MessageBubble({ 
  message, 
  onPlayAudio,
  showTimestamp = true,
  showPlayButton = false,
  className 
}: MessageBubbleProps) {
  const isAssistant = message.role === 'assistant';
  const isUser = message.role === 'user';

  const formatTime = (timestamp: Date | string) => {
    const date = typeof timestamp === 'string' ? new Date(timestamp) : timestamp;
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    });
  };

  return (
    <div className={cn(
      'flex gap-3 max-w-4xl',
      isUser && 'flex-row-reverse',
      className
    )}>
      {/* Avatar */}
      <div className={cn(
        'flex-shrink-0 h-8 w-8 rounded-full flex items-center justify-center',
        isAssistant ? 'bg-blue-100 text-blue-600' : 'bg-green-100 text-green-600'
      )}>
        {isAssistant ? (
          <Bot className="h-4 w-4" />
        ) : (
          <User className="h-4 w-4" />
        )}
      </div>

      {/* Message content */}
      <div className={cn(
        'flex-1 min-w-0',
        isUser && 'flex flex-col items-end'
      )}>
        {/* Header */}
        <div className={cn(
          'flex items-center gap-2 mb-1',
          isUser && 'flex-row-reverse'
        )}>
          <span className="text-sm font-semibold">
            {isAssistant ? 'AI Interviewer' : 'You'}
          </span>
          {showTimestamp && (
            <span className="text-xs text-muted-foreground">
              {formatTime(message.timestamp)}
            </span>
          )}
          {showPlayButton && isUser && onPlayAudio && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onPlayAudio(message.id)}
              className="h-6 w-6 p-0"
            >
              <Play className="h-3 w-3" />
            </Button>
          )}
        </div>

        {/* Message bubble */}
        <div className={cn(
          'rounded-lg px-4 py-2 max-w-[80%] break-words',
          isAssistant 
            ? 'bg-muted text-foreground' 
            : 'bg-primary text-primary-foreground'
        )}>
          <p className="text-sm leading-relaxed whitespace-pre-wrap">
            {message.content}
          </p>
          
          {/* Metadata */}
          {message.metadata && (
            <div className="mt-2 pt-2 border-t border-current/10">
              <div className="flex items-center gap-2 text-xs opacity-70">
                {message.metadata.transcriptionConfidence && (
                  <span>
                    Confidence: {Math.round(message.metadata.transcriptionConfidence * 100)}%
                  </span>
                )}
                {message.metadata.audioDuration && (
                  <span>
                    Duration: {message.metadata.audioDuration}s
                  </span>
                )}
                {message.metadata.isAiGenerated && (
                  <span className="flex items-center gap-1">
                    <Bot className="h-3 w-3" />
                    AI Generated
                  </span>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

/**
 * Props for MessageList component
 */
interface MessageListProps {
  messages: Message[];
  onPlayAudio?: (messageId: string) => void;
  showTimestamps?: boolean;
  showPlayButtons?: boolean;
  isLoading?: boolean;
  className?: string;
}

/**
 * List of messages component
 */
export function MessageList({ 
  messages, 
  onPlayAudio,
  showTimestamps = true,
  showPlayButtons = false,
  isLoading = false,
  className 
}: MessageListProps) {
  const messagesEndRef = React.useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when new messages arrive
  React.useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  return (
    <div className={cn(
      'flex flex-col space-y-4 p-4',
      className
    )}>
      {messages.map((message) => (
        <MessageBubble
          key={message.id}
          message={message}
          onPlayAudio={onPlayAudio}
          showTimestamp={showTimestamps}
          showPlayButton={showPlayButtons}
        />
      ))}
      
      {isLoading && (
        <div className="flex gap-3">
          <div className="flex-shrink-0 h-8 w-8 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center">
            <Bot className="h-4 w-4" />
          </div>
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-1">
              <span className="text-sm font-semibold">AI Interviewer</span>
            </div>
            <div className="bg-muted rounded-lg px-4 py-2 max-w-[80%]">
              <div className="flex items-center gap-2">
                <div className="flex gap-1">
                  <div className="w-2 h-2 bg-current rounded-full animate-bounce" style={{ animationDelay: '0ms' }} />
                  <div className="w-2 h-2 bg-current rounded-full animate-bounce" style={{ animationDelay: '150ms' }} />
                  <div className="w-2 h-2 bg-current rounded-full animate-bounce" style={{ animationDelay: '300ms' }} />
                </div>
                <span className="text-sm text-muted-foreground">Thinking...</span>
              </div>
            </div>
          </div>
        </div>
      )}
      
      <div ref={messagesEndRef} />
    </div>
  );
}

/**
 * Props for MessageInput component
 */
interface MessageInputProps {
  value: string;
  onChange: (value: string) => void;
  onSubmit: (e: React.FormEvent) => void;
  disabled?: boolean;
  placeholder?: string;
  className?: string;
}

/**
 * Message input component
 */
export function MessageInput({ 
  value, 
  onChange, 
  onSubmit,
  disabled = false,
  placeholder = "Type your message...",
  className 
}: MessageInputProps) {
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      onSubmit(e as any);
    }
  };

  return (
    <form onSubmit={onSubmit} className={cn('flex gap-2', className)}>
      <textarea
        value={value}
        onChange={(e) => onChange(e.target.value)}
        onKeyDown={handleKeyDown}
        disabled={disabled}
        placeholder={placeholder}
        rows={1}
        className={cn(
          'flex-1 min-h-[40px] max-h-32 px-3 py-2 border border-input rounded-md',
          'bg-background text-foreground placeholder:text-muted-foreground',
          'focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent',
          'disabled:opacity-50 disabled:cursor-not-allowed',
          'resize-none'
        )}
        style={{
          height: 'auto',
          minHeight: '40px'
        }}
        onInput={(e) => {
          const target = e.target as HTMLTextAreaElement;
          target.style.height = 'auto';
          target.style.height = `${Math.min(target.scrollHeight, 128)}px`;
        }}
      />
      <Button
        type="submit"
        disabled={disabled || !value.trim()}
        className="px-4 py-2 h-10"
      >
        Send
      </Button>
    </form>
  );
}
