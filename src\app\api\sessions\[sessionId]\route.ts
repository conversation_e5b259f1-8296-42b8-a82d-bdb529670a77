import { NextRequest, NextResponse } from 'next/server';
import { sessionService } from '@/services';
import { withApiErrorHandling } from '@/lib/error-handler';
import { ApiResponse, InterviewSession } from '@/types/interview';
import { validateOrThrow, validateInterviewSession } from '@/lib/validation';

/**
 * Get a specific interview session
 */
export const GET = withApiErrorHandling(
  async (req: NextRequest, { params }: { params: { sessionId: string } }) => {
    const session = await sessionService.getSession(params.sessionId);

    const response: ApiResponse<{ session: InterviewSession }> = {
      success: true,
      data: { session },
    };

    return NextResponse.json(response);
  },
  { action: 'Get Session' }
);

/**
 * Update a specific interview session
 */
export const PUT = withApiErrorHandling(
  async (req: NextRequest, { params }: { params: { sessionId: string } }) => {
    const updates = await req.json();

    // Validate updates if they contain session data
    if (updates.messages || updates.status) {
      validateOrThrow(updates, validateInterviewSession, 'Session updates');
    }

    // Handle status updates
    if (updates.status) {
      await sessionService.updateSessionStatus(
        params.sessionId,
        updates.status
      );
    }

    // Handle message updates
    if (updates.messages && Array.isArray(updates.messages)) {
      // For now, we'll handle this through the session service
      // In a more complex scenario, you might want to add individual messages
      const session = await sessionService.getSession(params.sessionId);
      // Update logic would go here
    }

    const response: ApiResponse<{ success: boolean }> = {
      success: true,
      data: { success: true },
    };

    return NextResponse.json(response);
  },
  { action: 'Update Session' }
);

/**
 * Delete a specific interview session
 */
export const DELETE = withApiErrorHandling(
  async (req: NextRequest, { params }: { params: { sessionId: string } }) => {
    await sessionService.deleteSession(params.sessionId);

    const response: ApiResponse<{ success: boolean }> = {
      success: true,
      data: { success: true },
    };

    return NextResponse.json(response);
  },
  { action: 'Delete Session' }
);
