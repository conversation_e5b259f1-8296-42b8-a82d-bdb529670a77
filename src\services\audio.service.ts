/**
 * Audio service for Deepgram speech-to-text and text-to-speech operations
 */

import { createClient } from '@deepgram/sdk';
import { TranscriptionResult, TTSOptions } from '@/types/interview';
import { ExternalServiceError, AudioError } from '@/types/errors';
import { validateTTSOptions } from '@/lib/validation';

/**
 * Speech-to-text configuration
 */
interface STTConfig {
  model: string;
  language: string;
  smartFormat: boolean;
  punctuate: boolean;
  diarize?: boolean;
  numerals?: boolean;
}

/**
 * Text-to-speech configuration
 */
interface TTSConfig {
  model: string;
  encoding?: string;
  sampleRate?: number;
}

/**
 * Audio service class for handling Deepgram operations
 */
export class AudioService {
  private client: any;
  private sttConfig: STTConfig;
  private ttsConfig: TTSConfig;

  constructor() {
    if (!process.env.DEEPGRAM_API_KEY) {
      throw new ExternalServiceError(
        'Deepgram',
        'API key not configured',
        'DEEPGRAM_API_KEY environment variable is required'
      );
    }

    this.client = createClient(process.env.DEEPGRAM_API_KEY);
    
    this.sttConfig = {
      model: 'nova-2',
      language: 'en-US',
      smartFormat: true,
      punctuate: true,
      diarize: false,
      numerals: true,
    };

    this.ttsConfig = {
      model: 'aura-asteria-en',
      encoding: 'linear16',
      sampleRate: 24000,
    };
  }

  /**
   * Convert speech to text
   */
  async speechToText(audioBuffer: ArrayBuffer): Promise<TranscriptionResult> {
    try {
      if (!audioBuffer || audioBuffer.byteLength === 0) {
        throw new AudioError('Audio buffer is empty or invalid');
      }

      const response = await this.client.listen.prerecorded.transcribeFile(
        Buffer.from(audioBuffer),
        {
          model: this.sttConfig.model,
          language: this.sttConfig.language,
          smart_format: this.sttConfig.smartFormat,
          punctuate: this.sttConfig.punctuate,
          diarize: this.sttConfig.diarize,
          numerals: this.sttConfig.numerals,
        }
      );

      const result = response.result;
      const channel = result.results?.channels?.[0];
      const alternative = channel?.alternatives?.[0];

      if (!alternative) {
        throw new AudioError('No transcription result received');
      }

      return {
        transcript: alternative.transcript || '',
        confidence: alternative.confidence,
        duration: result.metadata?.duration,
        language: this.sttConfig.language,
      };
    } catch (error) {
      if (error instanceof AudioError) {
        throw error;
      }
      throw new ExternalServiceError(
        'Deepgram STT',
        'Speech transcription failed',
        error instanceof Error ? error.message : String(error)
      );
    }
  }

  /**
   * Convert text to speech
   */
  async textToSpeech(options: TTSOptions): Promise<Uint8Array> {
    try {
      const validation = validateTTSOptions(options);
      if (!validation.isValid) {
        throw new AudioError(`Invalid TTS options: ${validation.errors.join(', ')}`);
      }

      const response = await this.client.speak.request(
        { text: options.text },
        {
          model: options.model || this.ttsConfig.model,
          encoding: this.ttsConfig.encoding,
          sample_rate: this.ttsConfig.sampleRate,
        }
      );

      const stream = await response.getStream();
      if (!stream) {
        throw new AudioError('No audio stream received from TTS service');
      }

      const reader = stream.getReader();
      const chunks: Uint8Array[] = [];
      
      try {
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;
          chunks.push(value);
        }
      } finally {
        reader.releaseLock();
      }

      if (chunks.length === 0) {
        throw new AudioError('Empty audio stream received');
      }

      // Combine chunks into single Uint8Array
      const totalLength = chunks.reduce((acc, chunk) => acc + chunk.length, 0);
      const audioBuffer = new Uint8Array(totalLength);
      let offset = 0;
      
      for (const chunk of chunks) {
        audioBuffer.set(chunk, offset);
        offset += chunk.length;
      }

      return audioBuffer;
    } catch (error) {
      if (error instanceof AudioError) {
        throw error;
      }
      throw new ExternalServiceError(
        'Deepgram TTS',
        'Text-to-speech generation failed',
        error instanceof Error ? error.message : String(error)
      );
    }
  }

  /**
   * Validate audio format
   */
  validateAudioFormat(audioBuffer: ArrayBuffer, expectedFormats: string[] = ['wav', 'mp3', 'flac']): boolean {
    if (!audioBuffer || audioBuffer.byteLength < 4) {
      return false;
    }

    const header = new Uint8Array(audioBuffer.slice(0, 4));
    
    // Check for common audio file signatures
    const signatures = {
      wav: [0x52, 0x49, 0x46, 0x46], // RIFF
      mp3: [0xFF, 0xFB], // MP3 frame header (partial)
      flac: [0x66, 0x4C, 0x61, 0x43], // fLaC
    };

    for (const format of expectedFormats) {
      const signature = signatures[format as keyof typeof signatures];
      if (signature && this.matchesSignature(header, signature)) {
        return true;
      }
    }

    return false;
  }

  /**
   * Check if header matches signature
   */
  private matchesSignature(header: Uint8Array, signature: number[]): boolean {
    if (header.length < signature.length) {
      return false;
    }

    for (let i = 0; i < signature.length; i++) {
      if (header[i] !== signature[i]) {
        return false;
      }
    }

    return true;
  }

  /**
   * Update STT configuration
   */
  updateSTTConfig(config: Partial<STTConfig>): void {
    this.sttConfig = { ...this.sttConfig, ...config };
  }

  /**
   * Update TTS configuration
   */
  updateTTSConfig(config: Partial<TTSConfig>): void {
    this.ttsConfig = { ...this.ttsConfig, ...config };
  }

  /**
   * Get current STT configuration
   */
  getSTTConfig(): STTConfig {
    return { ...this.sttConfig };
  }

  /**
   * Get current TTS configuration
   */
  getTTSConfig(): TTSConfig {
    return { ...this.ttsConfig };
  }

  /**
   * Validate service connectivity
   */
  async validateService(): Promise<boolean> {
    try {
      // Test with a small audio buffer (silence)
      const testBuffer = new ArrayBuffer(1024);
      await this.speechToText(testBuffer);
      return true;
    } catch (error) {
      return false;
    }
  }
}

// Export singleton instance
export const audioService = new AudioService();
