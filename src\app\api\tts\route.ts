import { NextRequest, NextResponse } from 'next/server';
import { audioService } from '@/services';
import { withApiErrorHandling } from '@/lib/error-handler';
import { validateTTSOptions } from '@/lib/validation';

/**
 * Handle text-to-speech generation
 */
export const POST = withApiErrorHandling(
  async (req: NextRequest) => {
    const requestData = await req.json();

    // Validate TTS options
    const validation = validateTTSOptions(requestData);
    if (!validation.isValid) {
      throw new Error(`Invalid TTS options: ${validation.errors.join(', ')}`);
    }

    // Generate audio
    const audioBuffer = await audioService.textToSpeech(validation.data!);

    return new NextResponse(audioBuffer, {
      headers: {
        'Content-Type': 'audio/wav',
        'Content-Length': audioBuffer.length.toString(),
      },
    });
  },
  { action: 'Text to Speech' }
);
