import { createClient } from '@deepgram/sdk';
import { NextRequest, NextResponse } from 'next/server';

export async function POST(req: NextRequest) {
  try {
    const { text } = await req.json();
    
    if (!process.env.DEEPGRAM_API_KEY) {
      return NextResponse.json({ error: 'Deepgram API key not configured' }, { status: 500 });
    }

    const deepgram = createClient(process.env.DEEPGRAM_API_KEY);
    
    const response = await deepgram.speak.request(
      { text },
      { model: 'aura-asteria-en' }
    );

    const stream = await response.getStream();
    if (stream) {
      const reader = stream.getReader();
      const chunks = [];
      
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;
        chunks.push(value);
      }
      
      const audioBuffer = new Uint8Array(chunks.reduce((acc, chunk) => acc + chunk.length, 0));
      let offset = 0;
      for (const chunk of chunks) {
        audioBuffer.set(chunk, offset);
        offset += chunk.length;
      }
      
      return new NextResponse(audioBuffer, {
        headers: {
          'Content-Type': 'audio/wav',
        },
      });
    } else {
      return NextResponse.json({ error: 'No audio stream received' }, { status: 500 });
    }
  } catch (error) {
    console.error('TTS Error:', error);
    return NextResponse.json({ error: 'TTS generation failed' }, { status: 500 });
  }
}