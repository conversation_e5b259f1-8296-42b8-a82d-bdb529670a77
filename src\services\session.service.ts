/**
 * Session service for managing interview sessions
 */

import { InterviewSession, Message, InterviewConfig, InterviewStatus } from '@/types/interview';
import { SessionError, ValidationError } from '@/types/errors';
import { databaseService } from './database.service';
import { aiService } from './ai.service';
import { validateMessage, validateSessionId } from '@/lib/validation';

/**
 * Session service class for handling interview session operations
 */
export class SessionService {
  /**
   * Create a new interview session
   */
  async createSession(config?: InterviewConfig): Promise<InterviewSession> {
    try {
      const sessionId = crypto.randomUUID();
      const now = new Date();

      const session: InterviewSession = {
        sessionId,
        messages: [],
        startTime: now,
        status: 'active',
        audioRecorded: config?.enableAudioRecording ?? false,
        metadata: {
          duration: 0,
          questionCount: 0,
          averageResponseTime: 0,
          interviewType: 'technical',
          ...config && {
            maxDuration: config.maxDuration,
            difficulty: config.difficulty,
            focusAreas: config.focusAreas,
            language: config.language,
          }
        }
      };

      const createdSession = await databaseService.createSession(session);
      
      // Add initial AI message
      const initialMessage: Message = {
        id: crypto.randomUUID(),
        role: 'assistant',
        content: this.getInitialMessage(config),
        timestamp: now,
        metadata: {
          isAiGenerated: true
        }
      };

      await this.addMessage(sessionId, initialMessage);
      
      return {
        ...createdSession,
        messages: [initialMessage]
      };
    } catch (error) {
      throw new SessionError(
        'Failed to create interview session',
        error instanceof Error ? error.message : String(error)
      );
    }
  }

  /**
   * Get session by ID
   */
  async getSession(sessionId: string): Promise<InterviewSession> {
    try {
      if (!validateSessionId(sessionId)) {
        throw new ValidationError('Invalid session ID format');
      }

      const session = await databaseService.getSession(sessionId);
      if (!session) {
        throw new SessionError('Session not found');
      }

      return session;
    } catch (error) {
      if (error instanceof SessionError || error instanceof ValidationError) {
        throw error;
      }
      throw new SessionError(
        'Failed to retrieve session',
        error instanceof Error ? error.message : String(error)
      );
    }
  }

  /**
   * Add message to session
   */
  async addMessage(sessionId: string, message: Message): Promise<void> {
    try {
      const validation = validateMessage(message);
      if (!validation.isValid) {
        throw new ValidationError(`Invalid message: ${validation.errors.join(', ')}`);
      }

      const success = await databaseService.addMessageToSession(sessionId, message);
      if (!success) {
        throw new SessionError('Failed to add message to session');
      }

      // Update session metadata
      await this.updateSessionMetadata(sessionId);
    } catch (error) {
      if (error instanceof SessionError || error instanceof ValidationError) {
        throw error;
      }
      throw new SessionError(
        'Failed to add message',
        error instanceof Error ? error.message : String(error)
      );
    }
  }

  /**
   * Update session status
   */
  async updateSessionStatus(sessionId: string, status: InterviewStatus): Promise<void> {
    try {
      const updates: Partial<InterviewSession> = { status };
      
      if (status === 'completed') {
        updates.endTime = new Date();
      }

      const success = await databaseService.updateSession(sessionId, updates);
      if (!success) {
        throw new SessionError('Session not found or update failed');
      }
    } catch (error) {
      if (error instanceof SessionError) {
        throw error;
      }
      throw new SessionError(
        'Failed to update session status',
        error instanceof Error ? error.message : String(error)
      );
    }
  }

  /**
   * Generate AI response for session
   */
  async generateAIResponse(sessionId: string): Promise<ReadableStream> {
    try {
      const session = await this.getSession(sessionId);
      
      const context = {
        sessionId,
        messageCount: session.messages.length,
        duration: session.endTime 
          ? Math.floor((session.endTime.getTime() - session.startTime.getTime()) / 1000)
          : Math.floor((Date.now() - session.startTime.getTime()) / 1000),
        focusAreas: session.metadata?.focusAreas,
        difficulty: session.metadata?.difficulty as 'beginner' | 'intermediate' | 'advanced'
      };

      return await aiService.generateResponse(session.messages, context);
    } catch (error) {
      if (error instanceof SessionError) {
        throw error;
      }
      throw new SessionError(
        'Failed to generate AI response',
        error instanceof Error ? error.message : String(error)
      );
    }
  }

  /**
   * Complete interview session
   */
  async completeSession(sessionId: string): Promise<InterviewSession> {
    try {
      await this.updateSessionStatus(sessionId, 'completed');
      
      // Generate interview summary
      const session = await this.getSession(sessionId);
      const summary = await aiService.generateInterviewSummary(session.messages);
      
      // Update session with summary
      await databaseService.updateSession(sessionId, {
        metadata: {
          ...session.metadata,
          summary
        }
      });

      return await this.getSession(sessionId);
    } catch (error) {
      if (error instanceof SessionError) {
        throw error;
      }
      throw new SessionError(
        'Failed to complete session',
        error instanceof Error ? error.message : String(error)
      );
    }
  }

  /**
   * Get all sessions with pagination
   */
  async getSessions(page: number = 1, limit: number = 20) {
    try {
      return await databaseService.getSessions(page, limit);
    } catch (error) {
      throw new SessionError(
        'Failed to retrieve sessions',
        error instanceof Error ? error.message : String(error)
      );
    }
  }

  /**
   * Delete session
   */
  async deleteSession(sessionId: string): Promise<void> {
    try {
      const success = await databaseService.deleteSession(sessionId);
      if (!success) {
        throw new SessionError('Session not found or deletion failed');
      }
    } catch (error) {
      if (error instanceof SessionError) {
        throw error;
      }
      throw new SessionError(
        'Failed to delete session',
        error instanceof Error ? error.message : String(error)
      );
    }
  }

  /**
   * Get session statistics
   */
  async getSessionStats() {
    try {
      return await databaseService.getSessionStats();
    } catch (error) {
      throw new SessionError(
        'Failed to retrieve session statistics',
        error instanceof Error ? error.message : String(error)
      );
    }
  }

  /**
   * Update session metadata based on current state
   */
  private async updateSessionMetadata(sessionId: string): Promise<void> {
    try {
      const session = await this.getSession(sessionId);
      const now = new Date();
      
      const duration = Math.floor((now.getTime() - session.startTime.getTime()) / 1000);
      const questionCount = session.messages.filter(m => m.role === 'assistant').length;
      
      // Calculate average response time (simplified)
      const userMessages = session.messages.filter(m => m.role === 'user');
      const averageResponseTime = userMessages.length > 1 
        ? duration / userMessages.length 
        : 0;

      await databaseService.updateSession(sessionId, {
        metadata: {
          ...session.metadata,
          duration,
          questionCount,
          averageResponseTime: Math.round(averageResponseTime)
        }
      });
    } catch (error) {
      // Don't throw here as this is a background update
      console.warn('Failed to update session metadata:', error);
    }
  }

  /**
   * Get initial AI message based on configuration
   */
  private getInitialMessage(config?: InterviewConfig): string {
    const difficulty = config?.difficulty || 'intermediate';
    const focusAreas = config?.focusAreas?.join(', ') || 'general software engineering';

    return `Hello! I'm your AI interviewer today. I'm excited to learn about your background and technical skills. 

This will be a ${difficulty}-level interview focusing on ${focusAreas}. Let's start with a brief introduction - could you tell me about yourself and what brings you to this interview?`;
  }
}

// Export singleton instance
export const sessionService = new SessionService();
