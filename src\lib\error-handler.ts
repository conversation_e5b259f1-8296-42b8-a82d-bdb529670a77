/**
 * Centralized error handling utilities
 */

import { NextResponse } from 'next/server';
import { 
  AppError, 
  ErrorResponse, 
  ErrorContext, 
  ErrorSeverity, 
  isAppError, 
  isOperationalError 
} from '@/types/errors';

/**
 * Logger interface for error logging
 */
interface Logger {
  error: (message: string, context?: any) => void;
  warn: (message: string, context?: any) => void;
  info: (message: string, context?: any) => void;
}

/**
 * Simple console logger implementation
 */
const consoleLogger: Logger = {
  error: (message: string, context?: any) => {
    console.error(`[ERROR] ${message}`, context ? JSON.stringify(context, null, 2) : '');
  },
  warn: (message: string, context?: any) => {
    console.warn(`[WARN] ${message}`, context ? JSON.stringify(context, null, 2) : '');
  },
  info: (message: string, context?: any) => {
    console.info(`[INFO] ${message}`, context ? JSON.stringify(context, null, 2) : '');
  }
};

/**
 * Global error handler configuration
 */
class ErrorHandler {
  private logger: Logger;

  constructor(logger: Logger = consoleLogger) {
    this.logger = logger;
  }

  /**
   * Handles errors and returns appropriate API response
   */
  public handleApiError(error: unknown, context?: ErrorContext): NextResponse<ErrorResponse> {
    const errorInfo = this.processError(error, context);
    
    // Log the error
    this.logError(errorInfo.error, {
      ...context,
      statusCode: errorInfo.statusCode,
      stack: errorInfo.error.stack
    });

    // Create error response
    const errorResponse: ErrorResponse = {
      success: false,
      error: errorInfo.message,
      details: errorInfo.details,
      statusCode: errorInfo.statusCode,
      timestamp: new Date().toISOString(),
      path: context?.action
    };

    return NextResponse.json(errorResponse, { 
      status: errorInfo.statusCode 
    });
  }

  /**
   * Processes error and extracts relevant information
   */
  private processError(error: unknown, context?: ErrorContext) {
    if (isAppError(error)) {
      return {
        error,
        message: error.message,
        details: error.details,
        statusCode: error.statusCode
      };
    }

    if (error instanceof Error) {
      // Handle known error types
      if (error.name === 'ValidationError') {
        return {
          error,
          message: 'Validation failed',
          details: error.message,
          statusCode: 400
        };
      }

      if (error.name === 'MongoError' || error.name === 'MongoServerError') {
        return {
          error,
          message: 'Database operation failed',
          details: process.env.NODE_ENV === 'development' ? error.message : undefined,
          statusCode: 500
        };
      }

      // Generic error
      return {
        error,
        message: error.message || 'An unexpected error occurred',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined,
        statusCode: 500
      };
    }

    // Unknown error type
    return {
      error: new Error('Unknown error'),
      message: 'An unexpected error occurred',
      details: process.env.NODE_ENV === 'development' ? String(error) : undefined,
      statusCode: 500
    };
  }

  /**
   * Logs error with appropriate level
   */
  private logError(error: Error, context?: any) {
    const severity = this.getErrorSeverity(error);
    
    const logContext = {
      name: error.name,
      message: error.message,
      stack: error.stack,
      ...context
    };

    switch (severity) {
      case ErrorSeverity.CRITICAL:
      case ErrorSeverity.HIGH:
        this.logger.error(`${error.name}: ${error.message}`, logContext);
        break;
      case ErrorSeverity.MEDIUM:
        this.logger.warn(`${error.name}: ${error.message}`, logContext);
        break;
      case ErrorSeverity.LOW:
        this.logger.info(`${error.name}: ${error.message}`, logContext);
        break;
    }
  }

  /**
   * Determines error severity
   */
  private getErrorSeverity(error: Error): ErrorSeverity {
    if (isAppError(error)) {
      if (error.statusCode >= 500) {
        return ErrorSeverity.HIGH;
      }
      if (error.statusCode >= 400) {
        return ErrorSeverity.MEDIUM;
      }
      return ErrorSeverity.LOW;
    }

    if (error.name === 'MongoError' || error.name === 'MongoServerError') {
      return ErrorSeverity.CRITICAL;
    }

    return ErrorSeverity.HIGH;
  }

  /**
   * Handles client-side errors
   */
  public handleClientError(error: unknown, context?: string): void {
    const errorInfo = this.processError(error);
    
    this.logger.error(`Client Error${context ? ` (${context})` : ''}: ${errorInfo.message}`, {
      error: errorInfo.error.message,
      stack: errorInfo.error.stack
    });
  }
}

// Global error handler instance
export const errorHandler = new ErrorHandler();

/**
 * Async wrapper that catches errors and handles them appropriately
 */
export function withErrorHandling<T extends any[], R>(
  fn: (...args: T) => Promise<R>,
  context?: ErrorContext
) {
  return async (...args: T): Promise<R> => {
    try {
      return await fn(...args);
    } catch (error) {
      if (isOperationalError(error)) {
        throw error; // Re-throw operational errors
      }
      
      // Log and convert to operational error
      errorHandler.handleClientError(error, context?.action);
      throw new AppError(
        'An unexpected error occurred',
        500,
        true,
        process.env.NODE_ENV === 'development' ? String(error) : undefined
      );
    }
  };
}

/**
 * API route wrapper for consistent error handling
 */
export function withApiErrorHandling<T extends any[]>(
  handler: (...args: T) => Promise<NextResponse>,
  context?: ErrorContext
) {
  return async (...args: T): Promise<NextResponse> => {
    try {
      return await handler(...args);
    } catch (error) {
      return errorHandler.handleApiError(error, context);
    }
  };
}
