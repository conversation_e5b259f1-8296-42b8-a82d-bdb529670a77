import { NextRequest, NextResponse } from 'next/server';
import { audioService } from '@/services';
import { withApiErrorHandling } from '@/lib/error-handler';
import { ApiResponse, TranscriptionResult } from '@/types/interview';

/**
 * Handle speech-to-text transcription
 */
export const POST = withApiErrorHandling(
  async (req: NextRequest) => {
    // Get audio data from request
    const audioBuffer = await req.arrayBuffer();

    if (!audioBuffer || audioBuffer.byteLength === 0) {
      throw new Error('No audio data provided');
    }

    // Validate audio format (optional)
    if (!audioService.validateAudioFormat(audioBuffer)) {
      throw new Error(
        'Invalid audio format. Please use WAV, MP3, or FLAC format.'
      );
    }

    // Transcribe audio
    const result = await audioService.speechToText(audioBuffer);

    const response: ApiResponse<TranscriptionResult> = {
      success: true,
      data: result,
    };

    return NextResponse.json(response);
  },
  { action: 'Speech to Text' }
);
