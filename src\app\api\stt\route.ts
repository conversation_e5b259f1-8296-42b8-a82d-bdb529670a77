import { createClient } from '@deepgram/sdk';
import { NextRequest, NextResponse } from 'next/server';

export async function POST(req: NextRequest) {
  try {
    if (!process.env.DEEPGRAM_API_KEY) {
      return NextResponse.json({ error: 'Deepgram API key not configured' }, { status: 500 });
    }

    const deepgram = createClient(process.env.DEEPGRAM_API_KEY);
    
    // Get audio data from request
    const audioBuffer = await req.arrayBuffer();
    
    const response = await deepgram.listen.prerecorded.transcribeFile(
      Buffer.from(audioBuffer),
      {
        model: 'nova-2',
        language: 'en-US',
        smart_format: true,
        punctuate: true,
      }
    );

    const transcript = response.result.results?.channels?.[0]?.alternatives?.[0]?.transcript || '';
    
    return NextResponse.json({ transcript });
  } catch (error) {
    console.error('STT Error:', error);
    return NextResponse.json({ error: 'Speech transcription failed' }, { status: 500 });
  }
}