/**
 * AI service for Google Generative AI integration
 */

import { google } from '@ai-sdk/google';
import { convertToCoreMessages, streamText, generateText } from 'ai';
import { Message } from '@/types/interview';
import { ExternalServiceError } from '@/types/errors';
import { sanitizeText } from '@/lib/validation';

/**
 * AI service configuration
 */
interface AIServiceConfig {
  model: string;
  maxTokens?: number;
  temperature?: number;
  topP?: number;
  topK?: number;
}

/**
 * Interview context for AI responses
 */
interface InterviewContext {
  sessionId: string;
  messageCount: number;
  duration?: number;
  focusAreas?: string[];
  difficulty?: 'beginner' | 'intermediate' | 'advanced';
}

/**
 * AI service class for handling Google Generative AI operations
 */
export class AIService {
  private config: AIServiceConfig;
  private systemPrompt: string;

  constructor(config: Partial<AIServiceConfig> = {}) {
    this.config = {
      model: 'gemini-2.0-flash-exp',
      maxTokens: 1000,
      temperature: 0.7,
      topP: 0.9,
      topK: 40,
      ...config
    };

    this.systemPrompt = this.buildSystemPrompt();
  }

  /**
   * Build the system prompt for the AI interviewer
   */
  private buildSystemPrompt(): string {
    return `You are a friendly but professional senior software engineer conducting a technical interview.

INTERVIEW GUIDELINES:
- Start with a brief introduction and ask the candidate to introduce themselves
- Ask a mix of technical and behavioral questions appropriate to the role
- Keep responses concise and conversational (2-3 sentences max)
- Dynamically ask follow-up questions based on the candidate's answers
- Do not repeat questions that have already been asked
- Focus on understanding problem-solving approach and technical knowledge
- Gradually increase difficulty based on candidate responses
- Be encouraging but maintain professional standards

QUESTION CATEGORIES:
1. Background and Experience
2. Technical Knowledge (algorithms, data structures, system design)
3. Problem-Solving and Coding
4. Behavioral and Situational
5. Company and Role Fit

RESPONSE STYLE:
- Professional yet approachable tone
- Clear and direct communication
- Provide constructive feedback when appropriate
- Ask one question at a time
- Show genuine interest in the candidate's responses

Remember: You are evaluating both technical skills and communication abilities.`;
  }

  /**
   * Generate AI response for interview conversation
   */
  async generateResponse(
    messages: Message[],
    context?: InterviewContext
  ): Promise<ReadableStream> {
    try {
      // Sanitize message content
      const sanitizedMessages = messages.map(msg => ({
        ...msg,
        content: sanitizeText(msg.content)
      }));

      // Convert to AI SDK format
      const coreMessages = convertToCoreMessages(sanitizedMessages);

      // Build context-aware system prompt
      const contextualPrompt = this.buildContextualPrompt(context);

      const result = await streamText({
        model: google(this.config.model),
        system: contextualPrompt,
        messages: coreMessages,
        maxTokens: this.config.maxTokens,
        temperature: this.config.temperature,
        topP: this.config.topP,
        topK: this.config.topK,
      });

      return result.toDataStreamResponse().body!;
    } catch (error) {
      throw new ExternalServiceError(
        'Google AI',
        'Failed to generate AI response',
        error instanceof Error ? error.message : String(error)
      );
    }
  }

  /**
   * Generate a single AI response (non-streaming)
   */
  async generateSingleResponse(
    messages: Message[],
    context?: InterviewContext
  ): Promise<string> {
    try {
      // Sanitize message content
      const sanitizedMessages = messages.map(msg => ({
        ...msg,
        content: sanitizeText(msg.content)
      }));

      // Convert to AI SDK format
      const coreMessages = convertToCoreMessages(sanitizedMessages);

      // Build context-aware system prompt
      const contextualPrompt = this.buildContextualPrompt(context);

      const result = await generateText({
        model: google(this.config.model),
        system: contextualPrompt,
        messages: coreMessages,
        maxTokens: this.config.maxTokens,
        temperature: this.config.temperature,
        topP: this.config.topP,
        topK: this.config.topK,
      });

      return result.text;
    } catch (error) {
      throw new ExternalServiceError(
        'Google AI',
        'Failed to generate AI response',
        error instanceof Error ? error.message : String(error)
      );
    }
  }

  /**
   * Build context-aware system prompt
   */
  private buildContextualPrompt(context?: InterviewContext): string {
    let prompt = this.systemPrompt;

    if (context) {
      prompt += '\n\nCONTEXT:';
      
      if (context.messageCount) {
        prompt += `\n- This is message #${context.messageCount} in the conversation`;
      }

      if (context.duration) {
        const minutes = Math.floor(context.duration / 60);
        prompt += `\n- Interview has been running for ${minutes} minutes`;
      }

      if (context.difficulty) {
        prompt += `\n- Target difficulty level: ${context.difficulty}`;
      }

      if (context.focusAreas && context.focusAreas.length > 0) {
        prompt += `\n- Focus areas: ${context.focusAreas.join(', ')}`;
      }

      // Add guidance based on conversation progress
      if (context.messageCount <= 2) {
        prompt += '\n- Focus on introduction and background questions';
      } else if (context.messageCount <= 6) {
        prompt += '\n- Move to technical knowledge questions';
      } else if (context.messageCount <= 10) {
        prompt += '\n- Include problem-solving scenarios';
      } else {
        prompt += '\n- Consider wrapping up with final questions';
      }
    }

    return prompt;
  }

  /**
   * Generate interview summary
   */
  async generateInterviewSummary(messages: Message[]): Promise<string> {
    try {
      const summaryPrompt = `Analyze this interview conversation and provide a brief summary including:
1. Key topics discussed
2. Candidate's strengths
3. Areas for improvement
4. Overall assessment

Keep the summary concise and professional.`;

      const result = await generateText({
        model: google(this.config.model),
        system: summaryPrompt,
        messages: convertToCoreMessages(messages),
        maxTokens: 500,
        temperature: 0.3,
      });

      return result.text;
    } catch (error) {
      throw new ExternalServiceError(
        'Google AI',
        'Failed to generate interview summary',
        error instanceof Error ? error.message : String(error)
      );
    }
  }

  /**
   * Validate API key and model availability
   */
  async validateService(): Promise<boolean> {
    try {
      const testResult = await generateText({
        model: google(this.config.model),
        prompt: 'Hello, this is a test.',
        maxTokens: 10,
      });

      return !!testResult.text;
    } catch (error) {
      throw new ExternalServiceError(
        'Google AI',
        'Service validation failed',
        error instanceof Error ? error.message : String(error)
      );
    }
  }

  /**
   * Update service configuration
   */
  updateConfig(newConfig: Partial<AIServiceConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  /**
   * Get current configuration
   */
  getConfig(): AIServiceConfig {
    return { ...this.config };
  }
}

// Export singleton instance
export const aiService = new AIService();
